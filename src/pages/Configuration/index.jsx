import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  Button,
  useTheme,
  CircularProgress,
  alpha,
  IconButton,
} from '@mui/material';
import { useProject } from '../../contexts/ProjectContext';
import ProjectDetail from '../ProjectDetail';
import LanguageIcon from '@mui/icons-material/Language';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SettingsIcon from '@mui/icons-material/Settings';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';

const Configuration = () => {
  const { selectedProject, loading, error, refreshProjects } = useProject();

  // Automatically retry loading projects if there's an error
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        refreshProjects();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [error, refreshProjects]);
  const theme = useTheme();

  return (
    <Box sx={{ width: '100%', p: { xs: 2, md: 3 } }}>
      <Box
        sx={{
          backgroundColor: alpha('#e8f5e9', 0.7),
          borderRadius: 2,
          p: 3,
          mb: 3,
          position: 'relative',
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            opacity: 0.1,
            transform: 'rotate(15deg)',
            zIndex: 0,
          }}
        >
          <SettingsIcon sx={{ fontSize: 160 }} />
        </Box>
        <Typography
          variant='h4'
          component='h1'
          gutterBottom
          sx={{
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <SettingsIcon sx={{ fontSize: 32, color: theme.palette.primary.main }} />
          Configuration
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant='h5' component='h1' sx={{ fontWeight: 600, mr: 2 }}>
                {selectedProject?.title || 'Project Details'}
              </Typography>
              <Box
                sx={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  fontWeight: 600,
                }}
              >
                Active
              </Box>
            </Box>
            <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
              Project ID: {selectedProject?.id}
            </Typography>
            {selectedProject?.description && (
              <Typography variant='body1'>{selectedProject.description}</Typography>
            )}
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
            <Button
              variant='outlined'
              startIcon={<EditIcon />}
              sx={{
                borderColor: '#28a745',
                color: '#28a745',
                '&:hover': {
                  borderColor: '#1e7e34',
                  backgroundColor: alpha('#28a745', 0.1),
                },
              }}
            >
              Edit Project Info
            </Button>

            <Box sx={{ display: 'flex', gap: 3, mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box component='span' sx={{ mr: 1, color: 'text.secondary' }}>
                  <LanguageIcon fontSize='small' />
                </Box>
                <Box>
                  <Typography variant='body2' color='text.secondary'>
                    Language
                  </Typography>
                  <Typography variant='body2'>{selectedProject?.language || 'Hindi'}</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box component='span' sx={{ mr: 1, color: 'text.secondary' }}>
                  <LocationOnIcon fontSize='small' />
                </Box>
                <Box>
                  <Typography variant='body2' color='text.secondary'>
                    Region
                  </Typography>
                  <Typography variant='body2'>{selectedProject?.region || 'Nashik'}</Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Main Content Card */}
      <Card
        elevation={2}
        sx={{
          mb: 4,
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          width: '100%',
        }}
      >
        {error && (
          <Box
            sx={{
              p: 3,
              backgroundColor: alpha(theme.palette.error.main, 0.05),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography color='error' sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                component='span'
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.error.main,
                  display: 'inline-block',
                }}
              ></Box>
              {error}
            </Typography>
            <IconButton
              color='primary'
              onClick={refreshProjects}
              disabled={loading}
              sx={{ ml: 2 }}
              aria-label='Refresh projects'
            >
              <RefreshIcon />
            </IconButton>
          </Box>
        )}

        {loading ? (
          <Box sx={{ p: 5, textAlign: 'center' }}>
            <CircularProgress size={40} thickness={4} />
            <Typography sx={{ mt: 2 }}>Loading project configuration...</Typography>
          </Box>
        ) : !selectedProject ? (
          <Box sx={{ p: 5, textAlign: 'center' }}>
            <Typography variant='h6' color='text.secondary' gutterBottom>
              No Project Selected
            </Typography>
            <Typography color='text.secondary'>
              Please select a project from the dropdown in the header to view its configuration.
            </Typography>
          </Box>
        ) : (
          <ProjectDetail projectId={selectedProject.id} />
        )}
      </Card>
    </Box>
  );
};

export default Configuration;
