import React, { useState, useEffect, useRef } from 'react';
import { Card, Box, Alert, alpha, useTheme, Button, Tabs, Tab, Typography } from '@mui/material';
import Personas from '../Personas';
import Prompts from '../Prompts';
import Questionnaires from '../Questionnaires';
import { useParams } from 'react-router-dom';
import { getProjectById } from '../../services/projectsService';
import { useProject } from '../../contexts/ProjectContext';
import PersonIcon from '@mui/icons-material/Person';
import ChatIcon from '@mui/icons-material/Chat';
import QuizIcon from '@mui/icons-material/Quiz';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import LanguageIcon from '@mui/icons-material/Language';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SettingsIcon from '@mui/icons-material/Settings';

// Define tab values for consistency
const TAB_PERSONAS = 'personas';
const TAB_PROMPTS = 'prompts';
const TAB_QUESTIONNAIRE = 'questionnaire';

const ProjectDetail = () => {
  const theme = useTheme();
  const { setCurrentProject } = useProject();
  const [activeTab, setActiveTab] = useState(TAB_PERSONAS);
  // Using destructuring to avoid unused variable warnings while keeping the setters
  const [, setError] = useState(null);
  const [, setLoading] = useState(false);
  const [projectData, setProjectData] = useState(null);

  // Refs for component methods
  const personasRef = useRef(null);
  const promptsRef = useRef(null);
  const questionnairesRef = useRef(null);

  const { id: urlProjectId } = useParams();
  const { selectedProjectId } = useProject();
  const projectId = urlProjectId || selectedProjectId;

  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId) return;

      setLoading(true);
      setError(null);
      try {
        const response = await getProjectById(projectId);
        setCurrentProject(response.data);
        setProjectData(response.data);
        setError(null);
      } catch (err) {
        console.error('Error loading project:', err);
        setError('Failed to load project details');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [projectId, setCurrentProject]);

  if (!projectId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='info'>
          Please select a project from the dropdown in the header to view its details.
        </Alert>
      </Box>
    );
  }

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle add button click based on current tab
  const handleAddButtonClick = () => {
    switch (activeTab) {
      case TAB_PERSONAS:
        if (personasRef.current && personasRef.current.handleOpenCreate) {
          personasRef.current.handleOpenCreate();
        }
        break;
      case TAB_PROMPTS:
        if (promptsRef.current && promptsRef.current.handleOpenCreate) {
          promptsRef.current.handleOpenCreate();
        }
        break;
      case TAB_QUESTIONNAIRE:
        if (questionnairesRef.current && questionnairesRef.current.handleOpenCreate) {
          questionnairesRef.current.handleOpenCreate();
        }
        break;
      default:
        break;
    }
  };

  // Get the appropriate add button text based on selected tab
  const getAddButtonText = () => {
    switch (activeTab) {
      case TAB_PERSONAS:
        return 'Add Persona';
      case TAB_PROMPTS:
        return 'Add Prompt';
      case TAB_QUESTIONNAIRE:
        return 'Add Questionnaire';
      default:
        return 'Add';
    }
  };

  return (
    <Box sx={{ width: '100%', margin: '0 auto' }}>
      {/* Project Info Card */}
      {/* Tab Navigation with Add Button */}
      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          mb: 2,
          position: 'relative',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box sx={{ flexGrow: 1 }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label='project tabs'>
            <Tab
              label='Personas'
              id='personas-tab'
              value={TAB_PERSONAS}
              aria-controls='personas-panel'
              icon={<PersonIcon fontSize='small' />}
              iconPosition='start'
            />
            <Tab
              label='Prompts'
              id='prompts-tab'
              value={TAB_PROMPTS}
              aria-controls='prompts-panel'
              icon={<ChatIcon fontSize='small' />}
              iconPosition='start'
            />
            <Tab
              label='Questionnaire'
              id='questionnaire-tab'
              value={TAB_QUESTIONNAIRE}
              aria-controls='questionnaire-panel'
              icon={<QuizIcon fontSize='small' />}
              iconPosition='start'
            />
          </Tabs>
        </Box>
        <Button
          variant='contained'
          startIcon={<AddIcon />}
          onClick={handleAddButtonClick}
          sx={{
            backgroundColor: '#28a745',
            color: 'white',
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: '#1e7e34',
            },
          }}
        >
          {getAddButtonText()}
        </Button>
      </Box>

      {/* Tab Content */}
      <Box>
        {activeTab === TAB_PERSONAS && (
          <Card
            sx={{
              p: 0,
              borderRadius: 2,
            }}
          >
            <Personas
              projectId={projectId}
              inProjectDetail={false}
              setError={setError}
              ref={personasRef}
            />
          </Card>
        )}
        {activeTab === TAB_PROMPTS && (
          <Card
            sx={{
              p: 0,
              borderRadius: 2,
            }}
          >
            <Prompts
              projectId={projectId}
              inProjectDetail={false}
              setError={setError}
              ref={promptsRef}
            />
          </Card>
        )}
        {activeTab === TAB_QUESTIONNAIRE && (
          <Card
            sx={{
              p: 0,
              borderRadius: 2,
              overflow: 'hidden',
            }}
          >
            <Questionnaires
              projectId={projectId}
              inProjectDetail={false}
              setError={setError}
              ref={questionnairesRef}
            />
          </Card>
        )}
      </Box>
    </Box>
  );
};

export default ProjectDetail;
