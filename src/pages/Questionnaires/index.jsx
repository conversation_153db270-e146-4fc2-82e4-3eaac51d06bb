import React, { useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import {
  listQuestionnaires,
  createQuestionnaire,
  updateQuestionnaire,
  deleteQuestionnaire,
  getQuestionnaireVersions,
  createQuestionnaireVersion,
  updateQuestionnaireVersion,
} from '../../services/questionnaireService';
import QuestionnaireFormModal from './QuestionnaireFormModal';
import {
  Link,
  Typography,
  Box,
  Alert,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import VersionsDialog from '../../components/VersionsDialog/VersionsDialog';
import { statusOptions } from '../../utils/Constants/Questionnaries';

const getColumnsBase = (handleOpenVersionsDialog) => [
  {
    headerName: 'Question Text',
    field: 'current_version.question_text',
    valueGetter: (params) => params.row.current_version?.question_text || '',
    renderCell: ({ row, value }) => (
      <Link
        component='button'
        variant='body2'
        sx={{
          color: '#28a745',
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline',
            color: '#1e7e34',
          },
        }}
        onClick={(e) => {
          e.stopPropagation();
          handleOpenVersionsDialog(row);
          return false;
        }}
      >
        {value}
      </Link>
    ),
  },
  // {
  //   headerName: 'Question Type',
  //   field: 'question_category',
  //   valueGetter: (params) => params.row.question_category || 'Village Detail',
  //   renderCell: ({ row, value }) => (
  //     <span style={{ color: '#495057', fontWeight: 500 }}>{value}</span>
  //   ),
  // },
  {
    headerName: 'Answer Type',
    field: 'current_version.question_type',
    valueGetter: (params) => params.row.current_version?.question_type || '',
    renderCell: ({ value }) => (
      <span style={{ color: '#6c757d', fontSize: '0.875rem' }}>{value}</span>
    ),
  },
  { headerName: 'Project ID', field: 'project_id' },
  {
    headerName: 'State/Status',
    field: 'status',
    renderCell: ({ row }) => {
      const status = row.status || 'Draft';
      const isActive = status === 'Active';
      const primaryColor = isActive ? '#28a745' : '#6c757d';
      const secondaryText = isActive ? 'Draft' : 'Archived';

      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <span
            style={{
              color: primaryColor,
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            {status}
          </span>
          <span
            style={{
              color: '#6c757d',
              fontSize: '0.75rem',
              fontWeight: 400,
            }}
          >
            ({secondaryText})
          </span>
        </div>
      );
    },
  },
  {
    headerName: 'Order No',
    field: 'current_version.order_no',
    valueGetter: (params) => params.row.current_version?.order_no || '',
  },
  {
    headerName: 'Version',
    field: 'current_version.version',
    valueGetter: (params) => params.row.current_version?.version || '',
  },
];

const Questionnaires = forwardRef(({ projectId, inProjectDetail = false, setError }, ref) => {
  const theme = useTheme();
  const [questionnaires, setQuestionnaires] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    project_id: projectId || 1,
    question_text: '',
    question_type: '',
    options: null,
    status: '',
    order_no: null,
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  const [versionsDialogOpen, setVersionsDialogOpen] = useState(false);
  const [selectedQuestionnaire, setSelectedQuestionnaire] = useState(null);
  const [questionnaireVersions, setQuestionnaireVersions] = useState([]);

  const fetchQuestionnaires = useCallback(
    async (page = 0, limit = 10) => {
      setLoading(true);
      setError(null);
      try {
        const params = { skip: page * limit, limit };

        if (projectId) {
          params.project_id = projectId;
        }

        const res = await listQuestionnaires(params);
        const items = res.items || [];
        setQuestionnaires(items);
        setTotalCount(res.metadata?.total ?? items.length);
      } catch (err) {
        const errorMsg = 'Failed to load questionnaires';
        setError(errorMsg);
      } finally {
        setLoading(false);
      }
    },
    [projectId]
  );

  useEffect(() => {
    fetchQuestionnaires(page, rowsPerPage);
  }, [fetchQuestionnaires, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalInitialValues({
      question_text: '',
      question_type: 'Text',
      order_no: 1,
      status: 'Draft',
    });
    setModalMode('create');
    setModalOpen(true);
  };

  // EDIT
  const handleOpenEdit = (questionnaire) => {
    setModalMode('edit');
    const formValues = {
      id: questionnaire.id,
      status: questionnaire.status || 'Draft',
    };
    setModalInitialValues(formValues);
    setModalOpen(true);
  };

  const handleDelete = async (questionnaireId) => {
    if (!window.confirm('Are you sure you want to delete this questionnaire?')) return;
    setActionLoading(true);
    setActionError(null);
    try {
      await deleteQuestionnaire(questionnaireId);
      fetchQuestionnaires(page, rowsPerPage);
    } catch (err) {
      console.error('Error deleting questionnaire:', err);
      setActionError('Failed to delete questionnaire');
    } finally {
      setActionLoading(false);
    }
  };

  // ROW CLICK - Navigate to questionnaire detail (if needed)
  const handleRowClick = (row) => {
    // Uncomment if you want to navigate to a detail page
    // if (row && row.id) {
    //   navigate(`/questionnaires/${row.id}`);
    // }
  };

  // SUBMIT (CREATE/EDIT)
  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'edit') {
        const changedFields = {};

        changedFields.id = values.id;

        Object.keys(values).forEach((key) => {
          if (JSON.stringify(values[key]) !== JSON.stringify(modalInitialValues[key])) {
            changedFields[key] = values[key];
          }
        });

        await updateQuestionnaire(values.id, changedFields);
      } else {
        // For create, ensure all required fields are present
        const createData = {
          question_text: values.question_text,
          question_type: values.question_type,
          order_no: values.order_no,
          status: values.status,
          project_id: values.project_id,
        };
        console.log('Creating questionnaire with data:', createData);
        await createQuestionnaire(createData);
      }
      setModalOpen(false);
      fetchQuestionnaires(page, rowsPerPage);
    } catch (err) {
      console.error('Error saving questionnaire:', err);
      setActionError('Failed to save questionnaire');
    } finally {
      setActionLoading(false);
    }
  };

  const handleOpenVersionsDialog = (questionnaire) => {
    setSelectedQuestionnaire(questionnaire);
    setVersionsDialogOpen(true);
    fetchQuestionnaireVersions(questionnaire.id);
  };

  const fetchQuestionnaireVersions = async (questionnaireId) => {
    setActionLoading(true);
    try {
      const response = await getQuestionnaireVersions(questionnaireId);
      setQuestionnaireVersions(response || []);
      return response;
    } catch (err) {
      console.error('Error loading questionnaire versions:', err);
      setActionError('Failed to load questionnaire versions');
      return [];
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreateQuestionnaireVersion = async (questionnaireId, versionData) => {
    setActionLoading(true);
    try {
      await createQuestionnaireVersion(questionnaireId, versionData);
      if (selectedQuestionnaire) {
        await fetchQuestionnaireVersions(selectedQuestionnaire.id);
        // Refresh questionnaires list to get the updated current_version
        fetchQuestionnaires(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error creating questionnaire version:', err);
      setActionError('Failed to create questionnaire version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateQuestionnaireVersion = async (versionId, versionData) => {
    setActionLoading(true);
    try {
      await updateQuestionnaireVersion(versionId, versionData);
      if (selectedQuestionnaire) {
        await fetchQuestionnaireVersions(selectedQuestionnaire.id);
        // Refresh questionnaires list to get the updated current_version
        fetchQuestionnaires(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error updating questionnaire version:', err);
      setActionError('Failed to update questionnaire version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const getColumns = () => {
    const columnsBase = getColumnsBase(handleOpenVersionsDialog);
    return [
      ...columnsBase,
      {
        headerName: 'Actions',
        field: 'actions',
        renderCell: ({ row }) => (
          <div style={{ display: 'flex', gap: 8 }}>
            <Button
              size='small'
              variant='contained'
              customColor='#28a745'
              onClick={() => handleOpenEdit(row)}
              sx={{
                backgroundColor: '#28a745',
                color: 'white',
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                '&:hover': {
                  backgroundColor: '#1e7e34',
                },
              }}
            >
              Edit
            </Button>
            <Button
              size='small'
              variant='contained'
              customColor='#28a745'
              onClick={() => handleOpenVersionsDialog(row)}
              sx={{
                backgroundColor: '#28a745',
                color: 'white',
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                '&:hover': {
                  backgroundColor: '#1e7e34',
                },
              }}
            >
              Versions
            </Button>
          </div>
        ),
      },
    ];
  };

  const columns = getColumns();

  // Empty state component
  const EmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        px: 2,
        textAlign: 'center',
      }}
    >
      <Typography variant='h6' gutterBottom>
        No Questionnaires Yet
      </Typography>
      <Typography variant='body2' color='text.secondary' sx={{ mb: 3, maxWidth: 450 }}>
        Questionnaires help you collect structured data from your users. Create your first
        questionnaire to get started.
      </Typography>
      <Button
        variant='contained'
        startIcon={<AddIcon />}
        onClick={handleOpenCreate}
        disabled={actionLoading}
      >
        Add Questionnaire
      </Button>
    </Box>
  );

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    handleOpenCreate,
  }));

  return (
    <Box>
      {inProjectDetail && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Button
            variant='contained'
            startIcon={<AddIcon />}
            onClick={handleOpenCreate}
            disabled={actionLoading}
            sx={{
              backgroundColor: '#28a745',
              color: 'white',
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              py: 1,
              '&:hover': {
                backgroundColor: '#1e7e34',
              },
            }}
          >
            Add Questionnaire
          </Button>
        </Box>
      )}

      {actionError && (
        <Alert severity='error' sx={{ mb: 3 }}>
          {actionError}
        </Alert>
      )}

      {questionnaires.length === 0 && !loading ? (
        <Paper
          elevation={0}
          sx={{
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <EmptyState />
        </Paper>
      ) : (
        <Paper
          elevation={0}
          sx={{
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <DataTable
            data={questionnaires}
            columns={columns}
            loading={loading || actionLoading}
            totalCount={
              questionnaires.length > 0 ? Math.max(totalCount, questionnaires.length) : totalCount
            }
            serverSidePagination
            onPageChange={handlePageChange}
            onRowsPerPageChange={(newRowsPerPage) => {
              handleRowsPerPageChange({ target: { value: newRowsPerPage } });
            }}
            emptyStateMessage={loading ? 'Loading...' : 'No questionnaires found'}
            searchEnabled={false}
          />
        </Paper>
      )}

      <QuestionnaireFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
        projectId={projectId}
      />

      {/* Versions Dialog */}
      {selectedQuestionnaire && (
        <VersionsDialog
          open={versionsDialogOpen}
          onClose={() => setVersionsDialogOpen(false)}
          versions={questionnaireVersions}
          entity={selectedQuestionnaire}
          fetchVersions={fetchQuestionnaireVersions}
          createVersion={handleCreateQuestionnaireVersion}
          updateVersion={handleUpdateQuestionnaireVersion}
          fields={[
            { name: 'question_text', label: 'Question Text', type: 'textarea', required: true },
            {
              name: 'question_type',
              label: 'Question Type',
              type: 'text',
              required: true,
            },
            { name: 'options', label: 'Options', type: 'textarea', required: false },
            { name: 'order_no', label: 'Order Number', type: 'number', required: true },
            {
              name: 'status',
              label: 'Status',
              type: 'select',
              required: true,
              options: statusOptions,
            },
            { name: 'change_notes', label: 'Change Notes', type: 'textarea', required: true },
          ]}
          entityType='questionnaire'
        />
      )}
    </Box>
  );
});

export default Questionnaires;
