import React, { useState, useEffect } from 'react';
import { Box, CssBaseline, useMediaQuery, useTheme } from '@mui/material';
import AppHeader from './AppHeader';
import Sidebar from './Sidebar';

/**
 * Main application layout component that wraps all pages except login
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render inside layout
 * @returns {JSX.Element} Layout component
 */
const Layout = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [open, setOpen] = useState(!isMobile);
  const [collapsed, setCollapsed] = useState(false);

  // Effect to handle screen size changes
  useEffect(() => {
    if (isMobile) {
      setOpen(false); // Close on mobile by default
      setCollapsed(false); // Reset collapsed state on mobile
    } else {
      setOpen(true); // Always open on desktop
    }
  }, [isMobile]);

  // Toggle drawer between full and collapsed state
  const handleDrawerToggle = () => {
    if (isMobile) {
      setOpen(!open);
    } else {
      // Desktop: toggle between full (icons + text) and collapsed (icons only)
      setCollapsed(!collapsed);
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <CssBaseline />

      {/* Header/AppBar with hamburger menu */}
      <AppHeader open={open} handleDrawerToggle={handleDrawerToggle} />

      <Box sx={{ display: 'flex', width: '100%' }}>
        {/* Collapsible sidebar navigation */}
        <Box
          sx={{
            width: isMobile ? (open ? 240 : 0) : collapsed ? 64 : 240,
            flexShrink: 0,
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            '& .MuiDrawer-paper': {
              width: isMobile ? (open ? 240 : 0) : collapsed ? 64 : 240,
              boxSizing: 'border-box',
              position: 'relative',
              height: 'calc(100vh - 64px)',
              [theme.breakpoints.down('sm')]: {
                height: 'calc(100vh - 56px)',
              },
            },
          }}
        >
          <Sidebar
            open={open}
            collapsed={collapsed}
            handleDrawerToggle={handleDrawerToggle}
            variant={isMobile ? 'temporary' : 'persistent'}
          />
        </Box>

        {/* Main content area */}
        <Box
          component='main'
          sx={{
            flexGrow: 1,
            p: 3,
            mt: 8, // Add margin to accommodate app bar
            overflow: 'auto',
            width: isMobile
              ? open
                ? 'calc(100% - 240px)'
                : '100%'
              : collapsed
                ? 'calc(100% - 64px)'
                : 'calc(100% - 240px)',
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
